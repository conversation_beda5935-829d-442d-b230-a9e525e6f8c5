#!/usr/bin/env node

/**
 * Test script to verify regular message deletion real-time functionality
 * This script tests if message deletion events are properly emitted via socket.io for regular messages
 */

import { io as Client } from 'socket.io-client';
import axios from 'axios';

const BASE_URL = 'http://localhost:5000';
const USER_ID = '683ad57c4b3101236e74fec4'; // Test user ID
const RECIPIENT_ID = '683ad58c4b3101236e74fee3'; // Test recipient ID

async function testRegularMessageDeletion() {
  console.log('🧪 Testing regular message deletion real-time functionality...\n');

  try {
    // Step 1: Connect to socket
    console.log('🔌 Connecting to socket...');
    
    const socket = Client(BASE_URL, {
      query: { userId: USER_ID },
      reconnection: false,
      timeout: 5000,
      transports: ['polling', 'websocket'],
    });

    await new Promise((resolve, reject) => {
      socket.on('connect', () => {
        console.log('✅ Socket connected:', socket.id);
        resolve();
      });

      socket.on('connect_error', (error) => {
        console.error('❌ Socket connection failed:', error.message);
        reject(error);
      });

      setTimeout(() => {
        reject(new Error('Socket connection timeout'));
      }, 5000);
    });

    // Step 2: Set up event listeners for message deletion
    console.log('\n📡 Setting up socket event listeners...');
    
    let messageDeletedForMeReceived = false;
    let messageDeletedReceived = false;
    let receivedEvents = [];

    socket.on('messageDeletedForMe', (data) => {
      console.log('✅ Received messageDeletedForMe event:', data);
      messageDeletedForMeReceived = true;
      receivedEvents.push({ type: 'messageDeletedForMe', data });
    });

    socket.on('messageDeleted', (data) => {
      console.log('✅ Received messageDeleted event:', data);
      messageDeletedReceived = true;
      receivedEvents.push({ type: 'messageDeleted', data });
    });

    // Step 3: Send a test message first
    console.log('\n📝 Sending a test message...');
    
    const messageResponse = await axios.post(`${BASE_URL}/api/messages`, {
      text: 'Test message for deletion',
      recipientId: RECIPIENT_ID,
      tempId: Date.now().toString()
    }, {
      headers: {
        'Cookie': 'jwt-sociality-session-1753616119762=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODNhZDU3YzRiMzEwMTIzNmU3NGZlYzQiLCJpYXQiOjE3NTM2MTYxMTksImV4cCI6MTc1NjIwODExOX0.VcleUNdsBwRr1H7wihglJszQjYwTbPAPD--W85HAioU'
      }
    });

    if (messageResponse.status !== 201) {
      throw new Error(`Failed to send message: ${messageResponse.status}`);
    }

    const messageId = messageResponse.data._id;
    console.log('✅ Message sent successfully:', messageId);

    // Step 4: Test "delete for me" functionality
    console.log('\n🗑️ Testing "delete for me" functionality...');
    
    const deleteForMeResponse = await axios.delete(`${BASE_URL}/api/messages/${messageId}`, {
      data: { deleteForEveryone: false },
      headers: {
        'Cookie': 'jwt-sociality-session-1753616119762=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODNhZDU3YzRiMzEwMTIzNmU3NGZlYzQiLCJpYXQiOjE3NTM2MTYxMTksImV4cCI6MTc1NjIwODExOX0.VcleUNdsBwRr1H7wihglJszQjYwTbPAPD--W85HAioU'
      }
    });

    if (deleteForMeResponse.status !== 200) {
      throw new Error(`Failed to delete message: ${deleteForMeResponse.status}`);
    }

    console.log('✅ Delete for me request successful');

    // Wait for socket events
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 5: Send another test message for "delete for everyone"
    console.log('\n📝 Sending another test message...');
    
    const messageResponse2 = await axios.post(`${BASE_URL}/api/messages`, {
      text: 'Test message for delete for everyone',
      recipientId: RECIPIENT_ID,
      tempId: Date.now().toString()
    }, {
      headers: {
        'Cookie': 'jwt-sociality-session-1753616119762=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODNhZDU3YzRiMzEwMTIzNmU3NGZlYzQiLCJpYXQiOjE3NTM2MTYxMTksImV4cCI6MTc1NjIwODExOX0.VcleUNdsBwRr1H7wihglJszQjYwTbPAPD--W85HAioU'
      }
    });

    const messageId2 = messageResponse2.data._id;
    console.log('✅ Second message sent successfully:', messageId2);

    // Step 6: Test "delete for everyone" functionality
    console.log('\n🗑️ Testing "delete for everyone" functionality...');
    
    const deleteForEveryoneResponse = await axios.delete(`${BASE_URL}/api/messages/${messageId2}`, {
      data: { deleteForEveryone: true },
      headers: {
        'Cookie': 'jwt-sociality-session-1753616119762=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODNhZDU3YzRiMzEwMTIzNmU3NGZlYzQiLCJpYXQiOjE3NTM2MTYxMTksImV4cCI6MTc1NjIwODExOX0.VcleUNdsBwRr1H7wihglJszQjYwTbPAPD--W85HAioU'
      }
    });

    if (deleteForEveryoneResponse.status !== 200) {
      throw new Error(`Failed to delete message for everyone: ${deleteForEveryoneResponse.status}`);
    }

    console.log('✅ Delete for everyone request successful');

    // Wait for socket events
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 7: Verify results
    console.log('\n📊 Test Results:');
    console.log(`   messageDeletedForMe event received: ${messageDeletedForMeReceived ? '✅' : '❌'}`);
    console.log(`   messageDeleted event received: ${messageDeletedReceived ? '✅' : '❌'}`);
    
    console.log('\n📋 All received events:');
    receivedEvents.forEach((event, index) => {
      console.log(`   ${index + 1}. ${event.type}:`, event.data);
    });

    if (messageDeletedForMeReceived && messageDeletedReceived) {
      console.log('\n🎉 All tests passed! Real-time message deletion is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Real-time message deletion may not be working properly.');
    }

    // Cleanup
    socket.disconnect();
    console.log('\n✅ Test completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testRegularMessageDeletion();
