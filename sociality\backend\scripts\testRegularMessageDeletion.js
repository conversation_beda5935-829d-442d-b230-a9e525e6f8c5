#!/usr/bin/env node

/**
 * Test script to verify regular message deletion functionality
 * This script tests both "delete for me" and "delete for everyone" in regular conversations
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import mongoose from 'mongoose';
import Message from '../models/messageModel.js';
import Conversation from '../models/conversationModel.js';
import User from '../models/userModel.js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.resolve(__dirname, '../../.env');
dotenv.config({ path: envPath });

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    process.exit(1);
  }
}

async function testRegularMessageDeletion() {
  console.log('🧪 Testing regular message deletion functionality...\n');

  try {
    await connectDB();

    // Create test users
    const user1Id = new mongoose.Types.ObjectId();
    const user2Id = new mongoose.Types.ObjectId();
    
    const testUser1 = new User({
      _id: user1Id,
      username: 'testuser1',
      fullName: 'Test User 1',
      email: '<EMAIL>',
      password: 'hashedpassword1'
    });

    const testUser2 = new User({
      _id: user2Id,
      username: 'testuser2',
      fullName: 'Test User 2',
      email: '<EMAIL>',
      password: 'hashedpassword2'
    });

    await testUser1.save();
    await testUser2.save();
    console.log('✅ Created test users');

    // Create a test conversation
    const conversationId = new mongoose.Types.ObjectId();
    const testConversation = new Conversation({
      _id: conversationId,
      participants: [user1Id, user2Id],
      lastMessage: {
        text: 'Test conversation',
        sender: user1Id
      }
    });

    await testConversation.save();
    console.log('✅ Created test conversation:', conversationId.toString());

    // Create test messages
    const message1Id = new mongoose.Types.ObjectId();
    const message2Id = new mongoose.Types.ObjectId();
    
    const testMessage1 = new Message({
      _id: message1Id,
      conversationId: conversationId,
      sender: user1Id,
      text: 'Test message 1 - to be deleted for me',
      deletedFor: [],
      deletedForEveryone: false
    });

    const testMessage2 = new Message({
      _id: message2Id,
      conversationId: conversationId,
      sender: user1Id,
      text: 'Test message 2 - to be deleted for everyone',
      deletedFor: [],
      deletedForEveryone: false
    });

    await testMessage1.save();
    await testMessage2.save();
    console.log('✅ Created test messages');
    console.log('   Message 1 ID:', message1Id.toString());
    console.log('   Message 2 ID:', message2Id.toString());

    // Test "delete for me" functionality
    console.log('\n🔍 Testing "delete for me" functionality...');
    
    // Simulate adding user to deletedFor array
    testMessage1.deletedFor.push(user1Id);
    await testMessage1.save();
    
    // Verify the message is marked as deleted for user1
    const deletedForMeMessage = await Message.findById(message1Id);
    
    if (deletedForMeMessage.deletedFor.includes(user1Id)) {
      console.log('✅ "Delete for me" functionality works correctly');
      console.log('   Message is in deletedFor array for user1');
    } else {
      console.log('❌ "Delete for me" functionality failed');
    }

    // Test "delete for everyone" functionality
    console.log('\n🔍 Testing "delete for everyone" functionality...');
    
    // Simulate marking message as deleted for everyone
    testMessage2.deletedForEveryone = true;
    await testMessage2.save();
    
    // Verify the message is marked as deleted for everyone
    const deletedForEveryoneMessage = await Message.findById(message2Id);
    
    if (deletedForEveryoneMessage.deletedForEveryone === true) {
      console.log('✅ "Delete for everyone" functionality works correctly');
      console.log('   Message is marked as deletedForEveryone: true');
    } else {
      console.log('❌ "Delete for everyone" functionality failed');
    }

    // Test message filtering (simulating frontend behavior)
    console.log('\n🔍 Testing message filtering logic...');
    
    const allMessages = await Message.find({
      conversationId: conversationId
    });
    
    // Filter messages for user1 (should exclude deleted for me)
    const messagesForUser1 = allMessages.filter(msg => 
      !msg.deletedFor.includes(user1Id) && !msg.deletedForEveryone
    );
    
    // Filter messages for user2 (should exclude deleted for everyone)
    const messagesForUser2 = allMessages.filter(msg => 
      !msg.deletedFor.includes(user2Id) && !msg.deletedForEveryone
    );
    
    console.log(`📊 Total messages: ${allMessages.length}`);
    console.log(`📊 Messages visible to user1: ${messagesForUser1.length}`);
    console.log(`📊 Messages visible to user2: ${messagesForUser2.length}`);
    
    if (messagesForUser1.length === 0 && messagesForUser2.length === 0) {
      console.log('✅ Message filtering works correctly');
    } else {
      console.log('⚠️ Message filtering may need adjustment');
    }

    // Test ID format consistency
    console.log('\n🔍 Testing ID format consistency...');
    
    console.log('📋 Message ID formats:');
    console.log('   Message 1 _id:', testMessage1._id.toString());
    console.log('   Message 1 _id type:', typeof testMessage1._id);
    console.log('   Message 2 _id:', testMessage2._id.toString());
    console.log('   Message 2 _id type:', typeof testMessage2._id);
    
    // Test socket event data format
    const socketEventData = {
      messageId: testMessage1._id.toString(),
      conversationId: testMessage1.conversationId.toString(),
      deleteForEveryone: false
    };
    
    console.log('📋 Socket event data format:');
    console.log('   messageId:', socketEventData.messageId);
    console.log('   conversationId:', socketEventData.conversationId);
    console.log('   deleteForEveryone:', socketEventData.deleteForEveryone);
    
    if (socketEventData.messageId && socketEventData.conversationId) {
      console.log('✅ Socket event data format is correct');
    } else {
      console.log('❌ Socket event data format has issues');
    }

    // Test user ID format consistency
    console.log('\n🔍 Testing user ID format consistency...');
    
    console.log('📋 User ID formats:');
    console.log('   User 1 ID:', user1Id.toString());
    console.log('   User 1 ID type:', typeof user1Id);
    console.log('   User 2 ID:', user2Id.toString());
    console.log('   User 2 ID type:', typeof user2Id);
    
    // Simulate sendMessageToUser call
    const userIdForSocket = user1Id.toString();
    console.log('📋 Socket user ID:', userIdForSocket);
    
    if (userIdForSocket && typeof userIdForSocket === 'string') {
      console.log('✅ User ID format for socket is correct');
    } else {
      console.log('❌ User ID format for socket has issues');
    }

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await Message.deleteMany({ conversationId: conversationId });
    await Conversation.deleteOne({ _id: conversationId });
    await User.deleteMany({ _id: { $in: [user1Id, user2Id] } });
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 Regular message deletion test completed successfully!');
    console.log('\n📝 Summary:');
    console.log('   - Regular messages use MongoDB ObjectId format');
    console.log('   - Socket events should include conversationId for filtering');
    console.log('   - User IDs should be converted to strings for socket emission');
    console.log('   - Message filtering logic works correctly');
    console.log('   - ID format consistency maintained');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the test
testRegularMessageDeletion();
