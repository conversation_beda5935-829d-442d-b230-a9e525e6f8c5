#!/usr/bin/env node

/**
 * Test script to verify socket connection functionality
 * This script tests if the socket.io server is working properly
 */

import { io as Client } from 'socket.io-client';

async function testSocketConnection() {
  console.log('🧪 Testing socket connection...\n');

  try {
    // Test direct connection to backend
    console.log('🔌 Testing direct connection to backend socket...');
    
    const socket = Client('http://localhost:5000', {
      query: { userId: '683ad57c4b3101236e74fec4' },
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 1000,
      timeout: 5000,
      transports: ['polling', 'websocket'],
    });

    // Set up event listeners
    socket.on('connect', () => {
      console.log('✅ Socket connected successfully!');
      console.log('   Socket ID:', socket.id);
      console.log('   Transport:', socket.io.engine.transport.name);
      
      // Test sending a verification event
      socket.emit('verifyConnection', { userId: '683ad57c4b3101236e74fec4' });
    });

    socket.on('connectionVerified', (data) => {
      console.log('✅ Connection verification successful:', data);
      
      // Test socket event emission
      console.log('\n🧪 Testing socket event emission...');
      
      // Simulate a message deletion event
      socket.emit('messageDeletedForMe', {
        messageId: 'test-message-id',
        conversationId: 'test-conversation-id',
        deleteForEveryone: false
      });
      
      console.log('✅ Socket event emitted successfully');
      
      // Close the connection after a short delay
      setTimeout(() => {
        socket.disconnect();
        console.log('\n✅ Socket connection test completed successfully!');
        process.exit(0);
      }, 2000);
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error.message);
      console.error('   Error type:', error.type);
      console.error('   Error description:', error.description);
      
      if (error.description) {
        console.error('   HTTP Status:', error.description);
      }
      
      process.exit(1);
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason);
    });

    socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Test timeout
    setTimeout(() => {
      if (!socket.connected) {
        console.error('❌ Socket connection timeout after 10 seconds');
        socket.disconnect();
        process.exit(1);
      }
    }, 10000);

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Test proxy connection through frontend port
async function testProxyConnection() {
  console.log('\n🧪 Testing proxy connection through frontend...\n');

  try {
    const socket = Client('http://localhost:7100', {
      query: { userId: '683ad57c4b3101236e74fec4' },
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 1000,
      timeout: 5000,
      transports: ['polling', 'websocket'],
    });

    socket.on('connect', () => {
      console.log('✅ Proxy socket connected successfully!');
      console.log('   Socket ID:', socket.id);
      console.log('   Transport:', socket.io.engine.transport.name);
      
      socket.disconnect();
      console.log('✅ Proxy connection test completed successfully!');
      
      // Now run the direct connection test
      testSocketConnection();
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Proxy socket connection error:', error.message);
      console.error('   Error type:', error.type);
      console.error('   Error description:', error.description);
      
      console.log('\n🔄 Proxy failed, trying direct connection...');
      
      // If proxy fails, try direct connection
      testSocketConnection();
    });

    // Test timeout for proxy
    setTimeout(() => {
      if (!socket.connected) {
        console.log('⚠️ Proxy connection timeout, trying direct connection...');
        socket.disconnect();
        testSocketConnection();
      }
    }, 5000);

  } catch (error) {
    console.error('\n❌ Proxy test failed:', error.message);
    console.log('🔄 Trying direct connection...');
    testSocketConnection();
  }
}

// Start with proxy test
testProxyConnection();
